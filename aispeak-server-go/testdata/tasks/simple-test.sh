#!/bin/bash

# 设置颜色
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

BASE_URL="http://localhost:8080/api/v1"

echo -e "${BLUE}=== AI-Speak 任务接口测试 ===${NC}"

# 测试1: 获取所有任务
echo -e "\n${BLUE}测试1: 获取所有任务${NC}"
response=$(curl -s -w "\n%{http_code}" -X GET "$BASE_URL/tasks")
http_code=$(echo "$response" | tail -n1)
body=$(echo "$response" | sed '$d')

echo "HTTP状态码: $http_code"
echo "响应内容: $body"

if [[ $http_code -eq 200 ]]; then
    echo -e "${GREEN}✓ 获取任务列表成功${NC}"
else
    echo -e "${RED}✗ 获取任务列表失败${NC}"
fi

# 测试2: 创建班级（任务创建的前置条件）
echo -e "\n${BLUE}测试2: 创建班级${NC}"
response=$(curl -s -w "\n%{http_code}" -X POST "$BASE_URL/classes" \
  -H "Content-Type: application/json" \
  -d '{
    "id": "class001",
    "name": "测试班级",
    "description": "用于测试的班级"
  }')

http_code=$(echo "$response" | tail -n1)
body=$(echo "$response" | sed '$d')

echo "HTTP状态码: $http_code"
echo "响应内容: $body"

if [[ $http_code -eq 201 || $http_code -eq 200 ]]; then
    echo -e "${GREEN}✓ 创建班级成功${NC}"
    class_created=true
else
    echo -e "${RED}✗ 创建班级失败${NC}"
    class_created=false
fi

# 测试3: 创建基本听写任务
echo -e "\n${BLUE}测试3: 创建基本听写任务${NC}"
if [[ $class_created == true ]]; then
    response=$(curl -s -w "\n%{http_code}" -X POST "$BASE_URL/tasks" \
      -H "Content-Type: application/json" \
      -d '{
        "title": "单词听写测试",
        "task_type": "dictation",
        "subject": "english",
        "status": "draft",
        "teacher_id": "teacher001",
        "class_id": "class001",
        "contents": [
          {
            "content_type": "dictation",
            "points": 100,
            "order_num": 1,
            "selected_word_ids": [1, 2, 3],
            "generate_mode": "manual"
          }
        ]
      }')

    http_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | sed '$d')

    echo "HTTP状态码: $http_code"
    echo "响应内容: $body"

    if [[ $http_code -eq 201 || $http_code -eq 200 ]]; then
        echo -e "${GREEN}✓ 创建听写任务成功${NC}"
        # 提取任务ID用于后续测试
        task_id=$(echo "$body" | grep -o '"id":[0-9]*' | cut -d':' -f2)
        echo "任务ID: $task_id"
    else
        echo -e "${RED}✗ 创建听写任务失败${NC}"
    fi
else
    echo -e "${RED}跳过任务创建测试（班级创建失败）${NC}"
fi

# 测试4: 验证测试 - 缺少必填字段
echo -e "\n${BLUE}测试4: 验证测试 - 缺少标题${NC}"
response=$(curl -s -w "\n%{http_code}" -X POST "$BASE_URL/tasks" \
  -H "Content-Type: application/json" \
  -d '{
    "task_type": "dictation",
    "subject": "english",
    "teacher_id": "teacher001",
    "class_id": "class001",
    "contents": [
      {
        "content_type": "dictation",
        "points": 100,
        "order_num": 1,
        "selected_word_ids": [1, 2, 3],
        "generate_mode": "manual"
      }
    ]
  }')

http_code=$(echo "$response" | tail -n1)
body=$(echo "$response" | sed '$d')

echo "HTTP状态码: $http_code"
echo "响应内容: $body"

if [[ $http_code -eq 400 ]]; then
    echo -e "${GREEN}✓ 验证测试成功（正确拒绝了缺少标题的请求）${NC}"
else
    echo -e "${RED}✗ 验证测试失败（应该返回400错误）${NC}"
fi

# 测试5: 验证测试 - 任务类型和内容类型不匹配
echo -e "\n${BLUE}测试5: 验证测试 - 任务类型和内容类型不匹配${NC}"
response=$(curl -s -w "\n%{http_code}" -X POST "$BASE_URL/tasks" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "类型不匹配测试",
    "task_type": "dictation",
    "subject": "english",
    "teacher_id": "teacher001",
    "class_id": "class001",
    "contents": [
      {
        "content_type": "sentence_repeat",
        "points": 100,
        "order_num": 1,
        "selected_sentence_ids": [1, 2, 3],
        "generate_mode": "manual"
      }
    ]
  }')

http_code=$(echo "$response" | tail -n1)
body=$(echo "$response" | sed '$d')

echo "HTTP状态码: $http_code"
echo "响应内容: $body"

if [[ $http_code -eq 400 ]]; then
    echo -e "${GREEN}✓ 验证测试成功（正确拒绝了类型不匹配的请求）${NC}"
else
    echo -e "${RED}✗ 验证测试失败（应该返回400错误）${NC}"
fi

# 测试6: 获取任务详情（如果任务创建成功）
if [[ -n "$task_id" ]]; then
  echo -e "\n${BLUE}测试6: 获取任务详情${NC}"
  response=$(curl -s -w "\n%{http_code}" -X GET "$BASE_URL/tasks/$task_id")
  http_code=$(echo "$response" | tail -n1)
  body=$(echo "$response" | sed '$d')

  echo "HTTP状态码: $http_code"
  echo "响应内容: $body"

  if [[ $http_code -eq 200 ]]; then
      echo -e "${GREEN}✓ 获取任务详情成功${NC}"
  else
      echo -e "${RED}✗ 获取任务详情失败${NC}"
  fi
fi

# 测试7: 测试提交任务（如果任务存在）
if [[ -n "$task_id" ]]; then
  echo -e "\n${BLUE}测试7: 提交任务${NC}"
  response=$(curl -s -w "\n%{http_code}" -X POST "$BASE_URL/tasks/$task_id/submissions" \
    -H "Content-Type: application/json" \
    -d '{
      "content_id": 1,
      "response": "测试回答",
      "media_files": [
        {
          "url": "https://example.com/audio.mp3",
          "type": "audio",
          "name": "recording.mp3"
        }
      ]
    }')

  http_code=$(echo "$response" | tail -n1)
  body=$(echo "$response" | sed '$d')

  echo "HTTP状态码: $http_code"
  echo "响应内容: $body"

  if [[ $http_code -eq 201 || $http_code -eq 200 ]]; then
      echo -e "${GREEN}✓ 提交任务成功${NC}"
  else
      echo -e "${RED}✗ 提交任务失败${NC}"
  fi
fi

# 测试8: 获取任务详情（如果任务创建成功）
if [[ -n "$task_id" ]]; then
  echo -e "\n${BLUE}测试8: 获取任务详情${NC}"
  response=$(curl -s -w "\n%{http_code}" -X GET "$BASE_URL/tasks/$task_id")
  http_code=$(echo "$response" | tail -n1)
  body=$(echo "$response" | sed '$d')

  echo "HTTP状态码: $http_code"
  echo "响应内容: $body"

  if [[ $http_code -eq 200 ]]; then
      echo -e "${GREEN}✓ 获取任务详情成功${NC}"
  else
      echo -e "${RED}✗ 获取任务详情失败${NC}"
  fi
fi

# 测试9: 测试提交任务（如果任务存在）
if [[ -n "$task_id" ]]; then
  echo -e "\n${BLUE}测试9: 提交任务${NC}"
  response=$(curl -s -w "\n%{http_code}" -X POST "$BASE_URL/tasks/$task_id/submissions" \
    -H "Content-Type: application/json" \
    -d '{
      "content_id": 1,
      "response": "apple, banana, cat",
      "media_files": [
        {
          "url": "https://example.com/audio.mp3",
          "type": "audio",
          "name": "recording.mp3"
        }
      ]
    }')

  http_code=$(echo "$response" | tail -n1)
  body=$(echo "$response" | sed '$d')

  echo "HTTP状态码: $http_code"
  echo "响应内容: $body"

  if [[ $http_code -eq 201 || $http_code -eq 200 ]]; then
      echo -e "${GREEN}✓ 提交任务成功${NC}"
      submission_id=$(echo "$body" | grep -o '"id":[0-9]*' | cut -d':' -f2)
      echo "提交ID: $submission_id"
  else
      echo -e "${RED}✗ 提交任务失败${NC}"
  fi
fi

# 测试10: 评分提交（如果提交成功）
if [[ -n "$submission_id" ]]; then
  echo -e "\n${BLUE}测试10: 评分提交${NC}"
  response=$(curl -s -w "\n%{http_code}" -X POST "$BASE_URL/submissions/$submission_id/grade" \
    -H "Content-Type: application/json" \
    -d '{
      "score": 85.5,
      "feedback": "很好！单词拼写正确，发音清晰。"
    }')

  http_code=$(echo "$response" | tail -n1)
  body=$(echo "$response" | sed '$d')

  echo "HTTP状态码: $http_code"
  echo "响应内容: $body"

  if [[ $http_code -eq 200 ]]; then
      echo -e "${GREEN}✓ 评分提交成功${NC}"
  else
      echo -e "${RED}✗ 评分提交失败${NC}"
  fi
fi

echo -e "\n${BLUE}=== 测试完成 ===${NC}"
