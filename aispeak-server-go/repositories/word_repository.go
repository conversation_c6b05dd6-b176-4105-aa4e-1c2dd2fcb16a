package repositories

import (
	"fmt"

	"github.com/gitzfp/ai-speak/aispeak-server-go/models"
	"gorm.io/gorm"
)

type WordRepository struct {
	DB *gorm.DB
}

func NewWordRepository(db *gorm.DB) *WordRepository {
	return &WordRepository{DB: db}
}

func (r *WordRepository) GetByIDs(ids []int32) ([]models.Word, error) {
    var words []models.Word
    // Import fmt package at the top of the file to fix undefined fmt error
    fmt.Printf("Executing SQL query: SELECT * FROM words WHERE id IN (%v)\n", ids)
    err := r.DB.Where("id IN ?", ids).Find(&words).Error
    return words, err
}

func (r *WordRepository) GetByLesson(bookID string, lessonID int) ([]models.Word, error) {
	var words []models.Word
	err := r.DB.Where("book_id = ? AND lesson_id = ?", bookID, lessonID).Find(&words).Error
	return words, err
}

func (r *WordRepository) Exists(id int32) (bool, error) {
    var count int64
    err := r.DB.Model(&models.Word{}).Where("id = ?", id).Count(&count).Error
    return count > 0, err
}

// GetFirst10Words 获取前10个单词用于测试
func (r *WordRepository) GetFirst10Words() ([]models.Word, error) {
    var words []models.Word
    err := r.DB.Limit(10).Find(&words).Error
    return words, err
}

// CreateTestWords 创建测试单词数据
func (r *WordRepository) CreateTestWords() error {
    testWords := []models.Word{
        {
            WordID:   1,
            LessonID: 1,
            BookID:   "test_book",
            Word:     stringPtr("apple"),
            Chinese:  stringPtr("苹果"),
            Phonetic: stringPtr("/ˈæpl/"),
        },
        {
            WordID:   2,
            LessonID: 1,
            BookID:   "test_book",
            Word:     stringPtr("banana"),
            Chinese:  stringPtr("香蕉"),
            Phonetic: stringPtr("/bəˈnænə/"),
        },
        {
            WordID:   3,
            LessonID: 1,
            BookID:   "test_book",
            Word:     stringPtr("cat"),
            Chinese:  stringPtr("猫"),
            Phonetic: stringPtr("/kæt/"),
        },
        {
            WordID:   4,
            LessonID: 1,
            BookID:   "test_book",
            Word:     stringPtr("dog"),
            Chinese:  stringPtr("狗"),
            Phonetic: stringPtr("/dɔːɡ/"),
        },
        {
            WordID:   5,
            LessonID: 1,
            BookID:   "test_book",
            Word:     stringPtr("elephant"),
            Chinese:  stringPtr("大象"),
            Phonetic: stringPtr("/ˈelɪfənt/"),
        },
    }

    for _, word := range testWords {
        if err := r.DB.Create(&word).Error; err != nil {
            return err
        }
    }
    return nil
}

// stringPtr 辅助函数，返回字符串指针
func stringPtr(s string) *string {
    return &s
}